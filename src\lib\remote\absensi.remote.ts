import { query } from '$app/server';
import { _Absensi, type Absensi } from '$lib/schema/collection';
import type { TipeAbsensi } from '$lib/schema/literal';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';

export const getAbsensi = query(async () => {
	const getter = effectfulFetch<Absensi[]>(`/protected/kehadiran/manual`);
	const response = await Effect.runPromise(getter);

	const data = response.kind === 'success' ? (response.data ?? []) : [_Absensi];

	const count = data.reduce(
		(acc: Record<TipeAbsensi, number>, cur: Absensi) => {
			acc[cur.tipe_absensi]++;
			return acc;
		},
		{ Hadir: 0, Izin: 0, Sakit: 0, Cuti: 0, Alpa: 0, Terlambat: 0 } as Record<TipeAbsensi, number>
	);

	if (response.kind === 'success') {
		return { data, count };
	} else {
		console.log(response);
		return { data, count };
	}
});
