<script lang="ts">
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import * as Avatar from '$lib/components/ui/avatar/index.js';

	import { page } from '$app/state';
	import { buttonVariants } from '$lib/components/ui/button';
	import Button from '$lib/components/ui/button/button.svelte';

	const { children } = $props();

	let user = {};
</script>

<Sidebar.Provider>
	<AppSidebar />
	<Sidebar.Inset>
		<header
			class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12"
		>
			<div class="flex w-full items-center gap-2 px-4">
				<Sidebar.Trigger class="-ml-1" />
				<Separator orientation="vertical" class="mr-2 data-[orientation=vertical]:h-4" />

				<h1>{page.data.headerTitle}</h1>

				<div class="grow"></div>

				<DropdownMenu.Root>
					<DropdownMenu.Trigger
						class={buttonVariants({ variant: 'ghost', size: 'lg', class: 'gap-4' })}
					>
						<Avatar.Root class="size-10 rounded-full">
							<!-- <Avatar.Image src={user.avatar} alt={user.name} /> -->
							<Avatar.Fallback class="rounded-lg">CN</Avatar.Fallback>
						</Avatar.Root>
						<div class="grid flex-1 gap-1 text-left text-sm leading-tight">
							<span class="truncate font-semibold">Nama User</span>
							<span class="truncate text-xs font-semibold text-primary">role user</span>
						</div>
					</DropdownMenu.Trigger>
				</DropdownMenu.Root>
			</div>
		</header>

		<Separator class="shadow" />

		<main class="flex-1 bg-muted/20 p-4 md:p-8 lg:p-16">
			<svelte:boundary>
				{#snippet pending()}{/snippet}

				{#snippet failed(error, reset)}
					<p class="text-destructive">Oops! {error}</p>
					<Button onclick={reset} variant="secondary">Reset</Button>
				{/snippet}

				{@render children?.()}
			</svelte:boundary>
		</main>
	</Sidebar.Inset>
</Sidebar.Provider>
