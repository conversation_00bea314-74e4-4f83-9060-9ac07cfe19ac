import { Schema } from 'effect';

export const TIPE_ABSENSI = ['<PERSON>ir', 'Izin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'] as const;
export type TipeAbsensi = (typeof TIPE_ABSENSI)[number];
export const TipeAbsensiSchema = Schema.Literal(...TIPE_ABSENSI);

export const STATUS_SINKRONISASI = ['Pending', 'Success', 'Failed'] as const;
export type StatusSinkronisasi = (typeof STATUS_SINKRONISASI)[number];
export const StatusSinkronisasiSchema = Schema.Literal(...STATUS_SINKRONISASI);

export const HARI_DALAM_SEMINGGU = [
	'Senin',
	'Selasa',
	'Rabu',
	'Ka<PERSON>',
	'Jumat',
	'Sabtu',
	'Minggu'
] as const;
export type HariDalamSeminggu = (typeof HARI_DALAM_SEMINGGU)[number];
export const HariDalamSemingguSchema = Schema.Literal(...HARI_DALAM_SEMINGGU);

export const JENIS_IZIN_PENGGUNA = ['Keluar_Meeting', 'Dinas_Luar', 'Tidak_Masuk'] as const;
export type JenisIzinPengguna = (typeof JENIS_IZIN_PENGGUNA)[number];
export const JenisIzinPenggunaSchema = Schema.Literal(...JENIS_IZIN_PENGGUNA);

export const STATUS_IZIN_PENGGUNA = ['Pending', 'Disetujui', 'Ditolak'] as const;
export type StatusIzinPengguna = (typeof STATUS_IZIN_PENGGUNA)[number];
export const StatusIzinPenggunaSchema = Schema.Literal(...STATUS_IZIN_PENGGUNA);
