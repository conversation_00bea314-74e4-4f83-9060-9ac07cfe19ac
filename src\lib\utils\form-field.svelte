<script lang="ts" generics="T extends string | number | Record<string, any>">
	import ValidationError from '$lib/utils/validation/validation-error.svelte';
	import type {
		HTMLInputAttributes,
		HTMLInputTypeAttribute,
		HTMLTextareaAttributes
	} from 'svelte/elements';
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/utils';
	import Input from '$lib/components/ui/input/input.svelte';

	interface IProps {
		name: string;
		id?: string;

		type: HTMLInputTypeAttribute | 'textarea' | 'select';
		value: T | null;
		class?: string;

		mode?: 'add' | 'view' | 'edit';

		noLabel?: boolean;
		label?: string;
		placeholder?: string;

		select_options?: Snippet;
		matcher?: keyof T;
		list?: T[];

		validation?: boolean;
		validationName?: string;

		asText?: boolean;
	}

	let {
		name,
		id = name,

		noLabel = false,
		label,
		placeholder = label,

		type,
		value = $bindable(),

		mode = $bindable('add'),

		class: className,

		select_options,
		matcher,
		list,

		validation = true,
		validationName,

		asText = false,

		...restProps
	}: IProps & Record<string, any> = $props();

	// Type-safe props for different element types
	const inputProps = $derived(
		type !== 'textarea' && type !== 'select' ? (restProps as HTMLInputAttributes) : {}
	);
	const textareaProps = $derived(type === 'textarea' ? (restProps as HTMLTextareaAttributes) : {});
</script>

{#if !asText}
	<label for={id} class:floating-label={!noLabel}>
		{#if !noLabel}
			<span>{label}</span>
		{/if}

		{#if type === 'textarea'}
			<textarea
				{name}
				{id}
				{placeholder}
				readonly={mode === 'view'}
				class={cn('textarea w-full', className ?? '')}
				class:border-dashed={mode === 'view'}
				bind:value
				{...textareaProps}
			></textarea>
		{:else if type === 'select'}
			{#key value}
				<!-- <Select
					{name}
					{id}
					bind:value
					{matcher}
					list={list ?? []}
					class={className ?? ''}
					{...restProps}
				>
					{@render select_options?.()}
				</Select> -->
			{/key}
		{:else if type === null}
			{#if type !== null}
				<Input
					{type}
					{name}
					{id}
					{placeholder}
					readonly={mode === 'view'}
					class={cn(`input w-full ${mode === 'view' ? 'border-dashed' : ''}`, className ?? '')}
					bind:value
					{...inputProps}
				/>
			{/if}
		{/if}

		{#if validation}
			<ValidationError name={validationName ?? name} />
		{/if}
	</label>
{:else}
	<p class="">{value}</p>
{/if}
