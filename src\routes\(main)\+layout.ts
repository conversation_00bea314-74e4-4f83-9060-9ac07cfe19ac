import type { LayoutLoad } from './$types';

const ROUTING_GROUP: { url: string; headerTitle: string; routeGroup: string }[] = [
	{ url: '/', headerTitle: 'Manajemen Absensi', routeGroup: 'Absensi' }
] as const;

export const load: LayoutLoad = async ({ url }) => {
	const headerTitle =
		ROUTING_GROUP.find((item) => item.url === url.pathname)?.headerTitle ??
		url.pathname.toUpperCase();

	const routeGroup =
		ROUTING_GROUP.find((item) => item.url === url.pathname)?.routeGroup ??
		url.pathname.split('/')[0];

	return {
		headerTitle,
		routeGroup
	};
};
