import { Schema } from 'effect';

const NUMBER_PATTERN = /^\d+$/;

export const OnlyNumberStringSchema = Schema.NonEmptyString.pipe(
	Schema.pattern(NUMBER_PATTERN)
).annotations({
	message: ({ actual }) => `The value must be number only, actual : ${actual}`
});

/////////////////////////////////////////////////////////////////////////////

const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const EmailSchema = Schema.Trimmed.pipe(Schema.pattern(EMAIL_PATTERN)).annotations({
	message: ({ actual }) => `Email is not valid, actual : ${actual}`
});

/////////////////////////////////////////////////////////////////////////////

const PHONE_NUMBER_PATTERN = /^\+\d{1,3}-\d{7,15}$/;

export const PhoneNumberSchema = Schema.NonEmptyString.annotations({
	message: () => 'Phone Number cannot be empty'
})
	.pipe(Schema.minLength(7, { message: () => 'Phone Number must be at least 7 digits' }))
	.pipe(Schema.maxLength(15, { message: () => 'Phone Number must be at most 15 digits' }))
	.pipe(
		Schema.pattern(PHONE_NUMBER_PATTERN, {
			message: () =>
				'Phone Number must be in the format +xx-xxxxxxxxxx, where +xx is the country code and xxxxxxxxxxxx is the phone number'
		})
	);

const USERNAME_PATTERN = /^[a-z0-9_]+$/;

export const UsernameSchema = Schema.NonEmptyTrimmedString.annotations({
	message: () => 'Username cannot be empty'
})
	.pipe(
		Schema.pattern(USERNAME_PATTERN, {
			message: ({ actual }) =>
				`Username can only contain lowercase letters, numbers, and underscores, actual: ${actual}`
		})
	)
	.pipe(
		Schema.minLength(4, {
			message: ({ actual }) => `Username must be at least 4 characters, actual: ${actual}`
		})
	);

export type Username = Schema.Schema.Type<typeof UsernameSchema>;
export const DEFAULT_USERNAME: Username = '';

export const PasswordSchema = Schema.NonEmptyString.annotations({
	message: () => 'Password cannot be empty'
}).pipe(Schema.minLength(6, { message: () => 'Password must be at least 6 characters' }));

export type Password = Schema.Schema.Type<typeof PasswordSchema>;
export const DEFAULT_PASSWORD: Password = '';
