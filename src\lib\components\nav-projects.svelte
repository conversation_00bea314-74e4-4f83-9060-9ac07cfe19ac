<script lang="ts">
	import { page } from '$app/state';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { useSidebar } from '$lib/components/ui/sidebar/context.svelte.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import EllipsisIcon from '@lucide/svelte/icons/ellipsis';
	import FolderIcon from '@lucide/svelte/icons/folder';
	import ForwardIcon from '@lucide/svelte/icons/forward';
	import Trash2Icon from '@lucide/svelte/icons/trash-2';

	let {
		projects
	}: {
		projects: {
			name: string;
			url: string;
			// This should be `Component` after @lucide/svelte updates types
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			icon: any;
		}[];
	} = $props();

	const sidebar = useSidebar();
</script>

<!-- <Sidebar.Group class="group-data-[collapsible=icon]:hidden"> -->
<Sidebar.Group>
	<Sidebar.GroupLabel>Manajemen</Sidebar.GroupLabel>
	<Sidebar.Menu class="space-y-1">
		{#each projects as item (item.name)}
			<Sidebar.MenuItem>
				<Sidebar.MenuButton
					class="rounded-full p-4 py-6 {page.data.routeGroup === item.name
						? 'bg-primary text-primary-foreground'
						: ''} hover:bg-primary/20"
				>
					{#snippet child({ props })}
						<a href={item.url} {...props}>
							<item.icon />
							<span>{item.name}</span>
						</a>
					{/snippet}
				</Sidebar.MenuButton>
			</Sidebar.MenuItem>
		{/each}
	</Sidebar.Menu>
</Sidebar.Group>
