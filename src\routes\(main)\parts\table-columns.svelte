<script lang="ts" module>
	import Button from '$lib/components/ui/button/button.svelte';
	import { renderSnippet } from '$lib/components/ui/data-table';
	import type { Absensi } from '$lib/schema/collection';
	import type { ColumnDef } from '@tanstack/table-core';
	import { createRawSnippet } from 'svelte';

	export const columns: ColumnDef<Absensi>[] = [
		{
			id: 'No',
			header: () => 'No.  ',
			cell: ({ row }) =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>${row.index + 1}</div>`
					}))
				)
		},
		{
			id: 'ID Pegawai',
			accessorKey: 'pengguna.id',
			header: () => 'ID Pegawai'
		},
		{
			id: 'Nama Pegawai',
			accessorKey: 'pengguna.nama_lengkap',
			header: () => 'Nama Pegawai'
		},
		{
			id: 'Status',
			accessorKey: 'tipe_absensi',
			header: () =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>Status</div>`
					}))
				),
			cell: ({ row }) => renderSnippet(StatusBadge, { status: row.original.tipe_absensi })
		},
		{
			id: 'Absen Masuk',
			accessorKey: 'waktu_masuk',
			header: () =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>Absen Masuk</div>`
					}))
				),
			cell: ({ row }) =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>${row.original.waktu_masuk}</div>`
					}))
				)
		},
		{
			id: 'Absen Pulang',
			accessorKey: 'waktu_pulang',
			header: () =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>Absen Pulang</div>`
					}))
				),
			cell: ({ row }) =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>${row.original.waktu_pulang}</div>`
					}))
				)
		},
		{
			id: 'Keterangan',
			accessorKey: 'catatan',
			header: () => 'Keterangan'
		},
		{
			id: 'Aksi',
			header: () => 'Aksi',
			cell: ({ row }) => renderSnippet(ActionButton, { row: row.original })
		}
	];
</script>

<script lang="ts">
	import PencilIcon from '@lucide/svelte/icons/pencil';
	import { type TipeAbsensi } from '$lib/schema/literal';
	import Badge from '$lib/components/ui/badge/badge.svelte';
</script>

{#snippet StatusBadge({ status }: { status: TipeAbsensi })}
	<div class="grid place-items-center">
		<Badge
			class="{status === 'Hadir'
				? 'bg-success/20 text-success'
				: status === 'Izin'
					? 'bg-primary/20 text-primary'
					: status === 'Sakit' || status === 'Cuti'
						? 'bg-warning/20 text-warning'
						: 'bg-error/20 text-error'} mx-auto px-4 py-2 font-bold"
		>
			{status}
		</Badge>
	</div>
{/snippet}

{#snippet ActionButton({ row }: { row: Absensi })}
	<Button variant="ghost" onclick={() => console.log(row)}>
		<PencilIcon />
	</Button>
{/snippet}
