<script lang="ts" module>
	import ChartPieIcon from '@lucide/svelte/icons/chart-pie';
	import FrameIcon from '@lucide/svelte/icons/frame';
	import MapIcon from '@lucide/svelte/icons/map';

	// This is sample data.
	const data = {
		user: {
			name: 'shadcn',
			email: '<EMAIL>',
			avatar: '/avatars/shadcn.jpg'
		},

		projects: [
			{
				name: 'Absensi',
				url: '/',
				icon: FrameIcon
			},
			{
				name: '<PERSON><PERSON>awa<PERSON>',
				url: '#',
				icon: ChartPieIcon
			},
			{
				name: 'Perizinan',
				url: '#',
				icon: MapIcon
			},
			{
				name: 'Lokasi',
				url: '#',
				icon: MapIcon
			}
		]
	};
</script>

<script lang="ts">
	import type { ComponentProps } from 'svelte';

	import NavProjects from './nav-projects.svelte';
	import NavUser from './nav-user.svelte';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';

	import Separator from './ui/separator/separator.svelte';
	import NavIdentity from './nav-identity.svelte';

	let {
		ref = $bindable(null),
		collapsible = 'icon',
		...restProps
	}: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root {collapsible} {...restProps}>
	<Sidebar.Header>
		<NavIdentity />
	</Sidebar.Header>

	<Separator />

	<Sidebar.Content>
		<NavProjects projects={data.projects} />
	</Sidebar.Content>

	<Sidebar.Footer>
		<NavUser user={data.user} />
	</Sidebar.Footer>

	<Sidebar.Rail />
</Sidebar.Root>
