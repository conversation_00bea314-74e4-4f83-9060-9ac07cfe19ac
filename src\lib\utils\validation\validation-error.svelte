<script lang="ts">
	import { getValidationErrorState } from './validation-error-state';

	interface IProps {
		name: string;
	}

	const { name }: IProps = $props();

	const validationErrorState = getValidationErrorState();
</script>

{#if !validationErrorState}
	<div class="text-error text-xs">Validation Error State is not set.</div>
{:else if validationErrorState.errors && validationErrorState.errors[name]}
	<ul class="validation-error text-error list-inside list-disc py-2 text-xs">
		{#each validationErrorState.errors[name] as error, i (error._tag + i)}
			<li>{error.message}</li>
		{/each}
	</ul>
{/if}
