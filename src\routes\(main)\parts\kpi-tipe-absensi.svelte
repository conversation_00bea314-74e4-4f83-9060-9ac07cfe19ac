<script lang="ts">
	import { getAbsensi } from '$lib/remote/absensi.remote';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import Badge from '$lib/components/ui/badge/badge.svelte';

	import CheckIcon from '@lucide/svelte/icons/check';
	import ClockIcon from '@lucide/svelte/icons/clock';
	import StampIcon from '@lucide/svelte/icons/stamp';
	import XIcon from '@lucide/svelte/icons/x';
	import CircleAlert from '@lucide/svelte/icons/circle-alert';

	import Icon from '@iconify/svelte';
</script>

<svelte:boundary>
	{#snippet failed(error)}
		Oops! {error}
	{/snippet}

	{#snippet pending()}
		<div class="grid auto-rows-fr grid-cols-6 gap-4">
			{#each Array(6) as _}
				<Skeleton class="aspect-video flex-1 rounded-lg" />
			{/each}
		</div>
	{/snippet}

	<div class="@container/kpi">
		<div
			class="grid auto-rows-fr grid-cols-2 gap-4 @sm/kpi:grid-cols-3 @md/kpi:grid-cols-4 @2xl/kpi:grid-cols-6"
		>
			{#each Object.entries((await getAbsensi()).count) as [tipe, count]}
				<div
					class="grid flex-1 grid-cols-2 gap-2 gap-x-4 rounded-lg border bg-white p-4 shadow-lg @3xl/kpi:grid-cols-4"
				>
					<div class="row-span-2">
						<Badge
							variant="secondary"
							class="aspect-square w-6 rounded-full p-1 text-primary-foreground {tipe === 'Hadir'
								? 'bg-success'
								: tipe === 'Cuti' || tipe === 'Sakit'
									? 'bg-warning'
									: tipe === 'Alpa' || tipe === 'Terlambat'
										? 'bg-destructive'
										: 'bg-primary'}"
						>
							{#if tipe === 'Hadir'}
								<CheckIcon />
							{:else if tipe === 'Izin'}
								<StampIcon />
							{:else if tipe === 'Cuti' || tipe === 'Sakit'}
								<CircleAlert />
							{:else if tipe === 'Alpa'}
								<XIcon />
							{:else if tipe === 'Terlambat'}
								<ClockIcon />
							{/if}
						</Badge>
					</div>
					<div class="col-span-3 text-base font-extrabold @3xl/kpi:text-lg">{tipe}</div>
					<!-- <div class="flex justify-center text-center">
					<Icon icon="mdi:account" font-size="1.2rem" color="lightgray" />
				</div> -->
					<div
						class="col-span-1 gap-1 text-xl leading-none font-semibold text-muted-foreground tabular-nums @3xl/kpi:col-span-3"
					>
						{count}
					</div>
				</div>
			{/each}
		</div>
	</div>
</svelte:boundary>
