import HttpStatusCode from './http_status_code';
import type { JSONResponse } from './types';
import { BackendError, FetchError, JSONError } from '$lib/utils/errors';

import { env } from '$env/dynamic/private';
import { getRequestEvent } from '$app/server';

import { Effect, pipe } from 'effect';

export const effectfulFetch = <T>(url: string, init: RequestInit = { method: 'GET' }) =>
	pipe(
		Effect.tryPromise({
			try: () => {
				const { fetch } = getRequestEvent();
				return fetch(env.API_HOST + ':' + env.API_PORT + env.SUB_PATH + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) => {
			return Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			});
		}),
		Effect.flatMap((json: JSONResponse<T>) => {
			console.log(`[${init.method}] ${url} - Backend Message : ${json.message}`);
			// console.log(json);

			if (json.status === HttpStatusCode.INTERNAL_SERVER_ERROR_500)
				return Effect.fail(new BackendError(json.status, json.message, [json.message]));
			else {
				let data: T | null = json.data?.items ?? null;
				let total_rows: number = json.data?.total_rows ?? 0;

				return Effect.succeed({ data, total_rows, kind: 'success' as const });
			}
		}),
		Effect.catchTags({
			FetchError: () =>
				Effect.succeed({
					status: HttpStatusCode.INTERNAL_SERVER_ERROR_500,
					message: `${url} : Failed to fetch data`,
					kind: 'fetch' as const
				}),
			JSONError: () =>
				Effect.succeed({
					status: HttpStatusCode.INTERNAL_SERVER_ERROR_500,
					message: `${url} : Failed to parse JSON`,
					kind: 'json' as const
				}),
			BackendError: (_BackendError) =>
				Effect.succeed({
					status: _BackendError.status,
					message: `${url} : ${_BackendError.message}`,
					errors: _BackendError.errors,
					kind: 'backend' as const
				})
		})
	);
