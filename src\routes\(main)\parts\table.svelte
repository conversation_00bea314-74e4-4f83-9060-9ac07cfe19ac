<script lang="ts">
	import DataTable from '$lib/components/data-table/data-table.svelte';
	import Skeleton from '$lib/components/ui/skeleton/skeleton.svelte';
	import { getAbsensi } from '$lib/remote/absensi.remote';
	import { columns } from './table-columns.svelte';
</script>

<svelte:boundary>
	{#snippet pending()}
		<Skeleton class="aspect-video flex-1 rounded-lg"></Skeleton>
	{/snippet}

	<DataTable {columns} data={(await getAbsensi()).data}></DataTable>
</svelte:boundary>
