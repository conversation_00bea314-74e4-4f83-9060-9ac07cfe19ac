import { env } from '$env/dynamic/private';
import type { HandleFetch } from '@sveltejs/kit';

export const handleFetch: HandleFetch = async ({ fetch, request }) => {
	if (request.url.startsWith(env.API_HOST + ':' + env.API_PORT)) {
		if (!request.headers.get('Content-Type'))
			request.headers.set('Content-Type', 'application/json; charset=utf-8');
		request.headers.set('Accept', '*/*');
	}

	return fetch(request);
};
