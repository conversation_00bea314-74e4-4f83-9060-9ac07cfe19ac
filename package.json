{"name": "abs<PERSON><PERSON>", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check ."}, "devDependencies": {"@dnd-kit-svelte/core": "^0.0.8", "@dnd-kit-svelte/modifiers": "^0.0.8", "@dnd-kit-svelte/sortable": "^0.0.8", "@dnd-kit-svelte/utilities": "^0.0.8", "@iconify/svelte": "^5.0.1", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-node": "^5.2.14", "@sveltejs/kit": "^2.28.0", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@tabler/icons-svelte": "^3.34.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/table-core": "^8.21.3", "@types/d3-scale": "^4.0.9", "@types/d3-shape": "^3.1.7", "bits-ui": "^2.9.2", "clsx": "^2.1.1", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "effect": "^3.17.7", "layerchart": "2.0.0-next.27", "oxlint": "^1.11.2", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.38.1", "svelte-check": "^4.3.1", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vaul-svelte": "^1.0.0-next.7", "vite": "^7.1.2", "vite-plugin-devtools-json": "^0.2.1", "zod": "^3.25.76"}}