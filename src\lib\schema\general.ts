import { Schema } from 'effect';

export const IdentitySchema = Schema.Struct({
	id: Schema.Number,
	uuid: Schema.UUID
});

export interface Identity extends Schema.Schema.Type<typeof IdentitySchema> {}
export interface IdentityEncoded extends Schema.Schema.Encoded<typeof IdentitySchema> {}

export const _Identity: Identity = {
	id: 0,
	uuid: '00000000-0000-0000-0000-000000000000'
};

////////////////////////////////////////////////////////////////////

export const DibuatSchema = Schema.Struct({
	dibuat_pada: Schema.String,
	dibuat_oleh: Schema.String
});

export interface Dibuat extends Schema.Schema.Type<typeof DibuatSchema> {}
export interface DibuatEncoded extends Schema.Schema.Encoded<typeof DibuatSchema> {}

export const _Dibuat: Dibuat = {
	dibuat_pada: new Date().toISOString(),
	dibuat_oleh: ''
};

////////////////////////////////////////////////////////////////////

export const DiperbaruiSchema = Schema.Struct({
	diperbarui_pada: Schema.NullOr(Schema.String),
	diperbarui_oleh: Schema.NullOr(Schema.String)
});

export interface Diperbarui extends Schema.Schema.Type<typeof DiperbaruiSchema> {}
export interface DiperbaruiEncoded extends Schema.Schema.Encoded<typeof DiperbaruiSchema> {}

export const _Diperbarui: Diperbarui = {
	diperbarui_pada: null,
	diperbarui_oleh: null
};

////////////////////////////////////////////////////////////////////

export const DihapusSchema = Schema.Struct({
	dihapus_pada: Schema.NullOr(Schema.String),
	dihapus_oleh: Schema.NullOr(Schema.String)
});

export interface Dihapus extends Schema.Schema.Type<typeof DihapusSchema> {}
export interface DihapusEncoded extends Schema.Schema.Encoded<typeof DihapusSchema> {}

export const _Dihapus: Dihapus = {
	dihapus_pada: null,
	dihapus_oleh: null
};

/////////////////////////////////////////////////////////////////////

export const ProvinceSchema = Schema.Struct({
	id: Schema.Number,
	name: Schema.String
});

export interface Province extends Schema.Schema.Type<typeof ProvinceSchema> {}
export interface ProvinceEncoded extends Schema.Schema.Encoded<typeof ProvinceSchema> {}

export const _Province: Province = {
	id: 0,
	name: ''
};

/////////////////////////////////////////////////////////////////////

export const CitySchema = Schema.Struct({
	id: Schema.Number,
	name: Schema.String,
	province_id: Schema.Number
});

export interface City extends Schema.Schema.Type<typeof CitySchema> {}
export interface CityEncoded extends Schema.Schema.Encoded<typeof CitySchema> {}

export const _City: City = {
	id: 0,
	name: '',
	province_id: 0
};

/////////////////////////////////////////////////////////////////////

export const SubDistrictSchema = Schema.Struct({
	id: Schema.Number,
	name: Schema.String,
	city_id: Schema.Number
});

export interface SubDistrict extends Schema.Schema.Type<typeof SubDistrictSchema> {}
export interface SubDistrictEncoded extends Schema.Schema.Encoded<typeof SubDistrictSchema> {}

export const _SubDistrict: SubDistrict = {
	id: 0,
	name: '',
	city_id: 0
};

/////////////////////////////////////////////////////////////////////

export const WardSchema = Schema.Struct({
	id: Schema.Number,
	name: Schema.String,
	sub_district_id: Schema.Number
});

export interface Ward extends Schema.Schema.Type<typeof WardSchema> {}
export interface WardEncoded extends Schema.Schema.Encoded<typeof WardSchema> {}

export const _Ward: Ward = {
	id: 0,
	name: '',
	sub_district_id: 0
};

/////////////////////////////////////////////////////////////////////
