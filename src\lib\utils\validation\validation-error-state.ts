import { getContext, setContext } from 'svelte';
import type { DecodeFormValueErrors } from '.';

export interface ValidationErrorState {
	errors: DecodeFormValueErrors;
}

export default class ValidationErrorStateClass implements ValidationErrorState {
	#errors = $state({});

	get errors() {
		return this.#errors;
	}

	set errors(errors: DecodeFormValueErrors) {
		this.#errors = errors;
	}
}

const V_ERROR_STATE_KEY = Symbol('@@validation-error-state@@');

export function setValidationErrorState(): void {
	setContext(V_ERROR_STATE_KEY, new ValidationErrorStateClass());
}

export function getValidationErrorState(): ValidationErrorState {
	return getContext<ValidationErrorState>(V_ERROR_STATE_KEY);
}
