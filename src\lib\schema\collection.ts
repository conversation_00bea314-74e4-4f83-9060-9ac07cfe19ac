import { Schema } from 'effect';
import {
	_Dibuat,
	_<PERSON><PERSON>pus,
	_<PERSON><PERSON><PERSON><PERSON>,
	_Identity,
	DibuatSchema,
	DihapusSchema,
	DiperbaruiSchema,
	IdentitySchema
} from './general';
import {
	DEFAULT_PASSWORD,
	DEFAULT_USERNAME,
	EmailSchema,
	PasswordSchema,
	PhoneNumberSchema,
	UsernameSchema
} from './utility';
import {
	HariDalamSemingguSchema,
	JenisIzinPenggunaSchema,
	StatusIzinPenggunaSchema,
	StatusSinkronisasiSchema,
	TipeAbsensiSchema
} from './literal';

export const KantorSchema = Schema.Struct({
	...IdentitySchema.fields,
	nama: Schema.NonEmptyString,
	kode: Schema.NonEmptyString,

	alamat: Schema.NullOr(Schema.NonEmptyString),
	nomor_telepon: Schema.NullOr(PhoneNumberSchema),
	email: Schema.NullOr(EmailSchema),

	area: Schema.String, // Temporary : Should be POLYGON ?,
	aktif: Schema.NullOr(Schema.Int),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface Kantor extends Schema.Schema.Type<typeof KantorSchema> {}
export interface KantorEncoded extends Schema.Schema.Encoded<typeof KantorSchema> {}

export const _Kantor: Kantor = {
	..._Identity,
	nama: '',
	kode: '',
	alamat: null,
	nomor_telepon: null,
	email: null,
	area: '',
	aktif: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

///////////////////////////////////////////////////////////////////

export const JadwalKantorSchema = Schema.Struct({
	...IdentitySchema.fields,
	uuid_kantor: Schema.UUID,

	hari_dalam_minggu: HariDalamSemingguSchema,
	jam_masuk_standar: Schema.String, // Temporary : Should be TIME ?
	jam_pulang_standar: Schema.String,

	toleransi_terlambat_menit: Schema.NullOr(Schema.Int),

	keterangan: Schema.NullOr(Schema.String),
	aktif: Schema.NullOr(Schema.Int),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface JadwalKantor extends Schema.Schema.Type<typeof JadwalKantorSchema> {}
export interface JadwalKantorEncoded extends Schema.Schema.Encoded<typeof JadwalKantorSchema> {}

export const _JadwalKantor: JadwalKantor = {
	..._Identity,
	uuid_kantor: '',
	hari_dalam_minggu: 'Senin',
	jam_masuk_standar: new Date().toUTCString(),
	jam_pulang_standar: new Date().toUTCString(),
	toleransi_terlambat_menit: null,
	keterangan: null,
	aktif: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const PenggunaSchema = Schema.Struct({
	...IdentitySchema.fields,

	username: UsernameSchema,
	hash_kata_sandi: PasswordSchema,

	nip: Schema.NonEmptyTrimmedString,
	nama_lengkap: Schema.NonEmptyString,

	email: Schema.NullOr(EmailSchema),
	nomor_telepon: Schema.NullOr(PhoneNumberSchema),

	uuid_kantor: Schema.NullOr(Schema.UUID),

	aktif: Schema.NullOr(Schema.Int),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface Pengguna extends Schema.Schema.Type<typeof PenggunaSchema> {}
export interface PenggunaEncoded extends Schema.Schema.Encoded<typeof PenggunaSchema> {}

export const _Pengguna: Pengguna = {
	..._Identity,
	username: DEFAULT_USERNAME,
	hash_kata_sandi: DEFAULT_PASSWORD,
	nip: '',
	nama_lengkap: 'John Doe',
	email: null,
	nomor_telepon: null,
	uuid_kantor: null,
	aktif: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const IzinPenggunaSchema = Schema.Struct({
	...IdentitySchema.fields,
	uuid_pengguna: Schema.UUID,
	uuid_jadwal_kantor: Schema.UUID,

	jenis_izin: JenisIzinPenggunaSchema,
	alasan: Schema.NullOr(Schema.String),

	nomor_surat: Schema.NullOr(Schema.NonEmptyString),
	berkas_pendukung: Schema.NullOr(Schema.NonEmptyString),

	tanggal_mulai: Schema.Date,
	tanggal_selesai: Schema.Date,

	status: StatusIzinPenggunaSchema,

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface IzinPengguna extends Schema.Schema.Type<typeof IzinPenggunaSchema> {}
export interface IzinPenggunaEncoded extends Schema.Schema.Encoded<typeof IzinPenggunaSchema> {}

export const _IzinPengguna: IzinPengguna = {
	..._Identity,
	uuid_pengguna: '',
	uuid_jadwal_kantor: '',
	jenis_izin: 'Tidak_Masuk',
	alasan: null,
	nomor_surat: null,
	berkas_pendukung: null,
	tanggal_mulai: new Date(),
	tanggal_selesai: new Date(),
	status: 'Pending',
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const AbsensiSchema = Schema.Struct({
	...IdentitySchema.fields,

	pengguna: PenggunaSchema,
	kantor: KantorSchema,

	tanggal_absensi: Schema.Date,
	waktu_masuk: Schema.NullOr(Schema.String), // Temporary : Should be TIME ?
	waktu_pulang: Schema.NullOr(Schema.String),

	tipe_absensi: TipeAbsensiSchema,
	catatan: Schema.NullOr(Schema.String),

	status_sinkronisasi: StatusSinkronisasiSchema,
	terakhir_disinkronkan_pada: Schema.NullOr(Schema.Date),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface Absensi extends Schema.Schema.Type<typeof AbsensiSchema> {}
export interface AbsensiEncoded extends Schema.Schema.Encoded<typeof AbsensiSchema> {}

export const _Absensi: Absensi = {
	..._Identity,

	pengguna: _Pengguna,
	kantor: _Kantor,

	tanggal_absensi: new Date(),
	waktu_masuk: '00:00',
	waktu_pulang: '00:05',
	tipe_absensi: 'Alpa',
	catatan: null,
	status_sinkronisasi: 'Pending',
	terakhir_disinkronkan_pada: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const NotifikasiSchema = Schema.Struct({
	...IdentitySchema.fields,
	uuid_pengguna: Schema.UUID,

	judul: Schema.NonEmptyString,
	konten: Schema.NullOr(Schema.String),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface Notifikasi extends Schema.Schema.Type<typeof NotifikasiSchema> {}
export interface NotifikasiEncoded extends Schema.Schema.Encoded<typeof NotifikasiSchema> {}

export const _Notifikasi: Notifikasi = {
	..._Identity,
	uuid_pengguna: '',
	judul: '',
	konten: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const RefreshTokenSchema = Schema.Struct({
	...IdentitySchema.fields,
	uuid_pengguna: Schema.UUID,

	token_hash: Schema.NonEmptyString,
	kadaluwarsa_pada: Schema.Date,

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface RefreshToken extends Schema.Schema.Type<typeof RefreshTokenSchema> {}
export interface RefreshTokenEncoded extends Schema.Schema.Encoded<typeof RefreshTokenSchema> {}

export const _RefreshToken: RefreshToken = {
	..._Identity,
	uuid_pengguna: '',
	token_hash: '',
	kadaluwarsa_pada: new Date(),
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const PeranMetadataSchema = Schema.Struct({
	...IdentitySchema.fields,

	nama_peran_casbin: Schema.NonEmptyString,
	nama_tampilan: Schema.NonEmptyString,

	deskripsi: Schema.NullOr(Schema.String),

	uuid_kantor: Schema.UUID,

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface PeranMetadata extends Schema.Schema.Type<typeof PeranMetadataSchema> {}
export interface PeranMetadataEncoded extends Schema.Schema.Encoded<typeof PeranMetadataSchema> {}

export const _PeranMetadata: PeranMetadata = {
	..._Identity,
	nama_peran_casbin: '',
	nama_tampilan: '',
	deskripsi: null,
	uuid_kantor: '',
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const IzinMetadataSchema = Schema.Struct({
	...IdentitySchema.fields,

	objek_casbin: Schema.NonEmptyString,
	aksi_casbin: Schema.NonEmptyString,
	nama_tampilan: Schema.NonEmptyString,

	deskripsi: Schema.NullOr(Schema.String),

	...DibuatSchema.fields,
	...DiperbaruiSchema.fields,
	...DihapusSchema.fields
});

export interface IzinMetadata extends Schema.Schema.Type<typeof IzinMetadataSchema> {}
export interface IzinMetadataEncoded extends Schema.Schema.Encoded<typeof IzinMetadataSchema> {}

export const _IzinMetadata: IzinMetadata = {
	..._Identity,
	objek_casbin: '',
	aksi_casbin: '',
	nama_tampilan: '',
	deskripsi: null,
	..._Dibuat,
	..._Diperbarui,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const RolePolicyBackupSchema = Schema.Struct({
	id: Schema.Number,
	role_uuid: Schema.UUID,
	domain_uuid: Schema.UUID,

	objek_casbin: Schema.NonEmptyString,
	aksi_casbin: Schema.NonEmptyString,

	backup_ke: Schema.NullOr(Schema.String),

	...DihapusSchema.fields
});

export interface RolePolicyBackup extends Schema.Schema.Type<typeof RolePolicyBackupSchema> {}
export interface RolePolicyBackupEncoded
	extends Schema.Schema.Encoded<typeof RolePolicyBackupSchema> {}

export const _RolePolicyBackup: RolePolicyBackup = {
	id: 0,
	role_uuid: '',
	domain_uuid: '',
	objek_casbin: '',
	aksi_casbin: '',
	backup_ke: null,
	..._Dihapus
};

/////////////////////////////////////////////////////////////////////

export const CasbinAturanSchema = Schema.Struct({
	id: Schema.Number,
	ptype: Schema.NonEmptyString,

	v0: Schema.NonEmptyString,
	v1: Schema.NonEmptyString,
	v2: Schema.NonEmptyString,
	v3: Schema.NonEmptyString,
	v4: Schema.NonEmptyString,
	v5: Schema.NonEmptyString
});

export interface CasbinAturan extends Schema.Schema.Type<typeof CasbinAturanSchema> {}
export interface CasbinAturanEncoded extends Schema.Schema.Encoded<typeof CasbinAturanSchema> {}

export const _CasbinAturan: CasbinAturan = {
	id: 0,
	ptype: '',
	v0: '',
	v1: '',
	v2: '',
	v3: '',
	v4: '',
	v5: ''
};

/////////////////////////////////////////////////////////////////////

export const OtpSchema = Schema.Struct({
	...IdentitySchema.fields,

	email: EmailSchema,
	kode_otp_hash: Schema.NonEmptyString,

	kadaluwarsa_pada: Schema.Date,

	...DibuatSchema.fields
});

export interface Otp extends Schema.Schema.Type<typeof OtpSchema> {}
export interface OtpEncoded extends Schema.Schema.Encoded<typeof OtpSchema> {}

export const _Otp: Otp = {
	..._Identity,
	email: '',
	kode_otp_hash: '',
	kadaluwarsa_pada: new Date(),
	..._Dibuat
};

/////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////////////
